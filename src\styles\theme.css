/* Primary Color Palette Theme - Overrides for consistent application */
:root {
  /* Primary Colors - These override the HSL values in index.css */
  --primary-hex: #97005e;
  --primary-dark-hex: #64003e;
  --primary-darker-hex: #4a002e;
  --primary-light-hex: #b8006e;
  --primary-lighter-hex: #c4007a;
  
  /* Primary Color Variations */
  --primary-50: #fdf2f8;
  --primary-100: #fce7f3;
  --primary-200: #fbcfe8;
  --primary-300: #f9a8d4;
  --primary-400: #f472b6;
  --primary-500: #97005e;
  --primary-600: #64003e;
  --primary-700: #4a002e;
  --primary-800: #3d0025;
  --primary-900: #2d001c;
  
  /* Opacity Variants */
  --primary-10: rgba(151, 0, 94, 0.1);
  --primary-20: rgba(151, 0, 94, 0.2);
  --primary-30: rgba(151, 0, 94, 0.3);
  --primary-40: rgba(151, 0, 94, 0.4);
  --primary-50-opacity: rgba(151, 0, 94, 0.5);
  
  /* Complementary Colors */
  --accent: #e879f9;
  --accent-light: #f3e8ff;
  --accent-dark: #a21caf;
  
  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: var(--primary);
}

/* Custom Button Styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border: none;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px 0 var(--primary-20);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-darker));
  box-shadow: 0 6px 20px 0 var(--primary-30);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px 0 var(--primary-20);
}

.btn-primary:disabled {
  background: var(--gray-400);
  box-shadow: none;
  transform: none;
  cursor: not-allowed;
}

/* Custom Input Styles */
.input-primary {
  border: 2px solid var(--gray-200);
  transition: all 0.2s ease;
  background: rgba(249, 250, 251, 0.5);
}

.input-primary:hover {
  border-color: var(--primary-lighter);
}

.input-primary:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-10);
  outline: none;
}

/* Custom Select Styles */
.select-primary {
  border: 2px solid var(--gray-200);
  transition: all 0.2s ease;
  background: rgba(249, 250, 251, 0.5);
}

.select-primary:hover {
  border-color: var(--primary-lighter);
}

.select-primary:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-10);
}

/* Custom Card Styles */
.card-primary {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid var(--primary-10);
  box-shadow: 
    0 25px 50px -12px var(--primary-10),
    0 0 0 1px var(--primary-10);
}

/* Link Styles */
.link-primary {
  color: var(--primary);
  transition: color 0.2s ease;
}

.link-primary:hover {
  color: var(--primary-dark);
}

/* Accent Elements */
.accent-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary);
}

.accent-dot-light {
  background: var(--primary-light);
}

.accent-dot-dark {
  background: var(--primary-dark);
}

/* Gradient Backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
}

.bg-gradient-primary-light {
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
}

/* Text Colors */
.text-primary {
  color: var(--primary);
}

.text-primary-dark {
  color: var(--primary-dark);
}

.text-primary-light {
  color: var(--primary-light);
}

/* Border Colors */
.border-primary {
  border-color: var(--primary);
}

.border-primary-light {
  border-color: var(--primary-lighter);
}

/* Shadow Utilities */
.shadow-primary {
  box-shadow: 0 4px 14px 0 var(--primary-20);
}

.shadow-primary-lg {
  box-shadow: 0 10px 25px -3px var(--primary-20), 0 4px 6px -2px var(--primary-10);
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
  .card-primary {
    margin: 1rem;
    border-radius: 1rem;
  }
}

/* Focus Visible for Accessibility */
.focus-primary:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Loading States */
.loading-primary {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--primary-50) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Force Primary Color Application - Override any conflicting styles */
.text-primary,
.text-primary * {
  color: hsl(327 100% 29%) !important;
}

.text-accent,
.text-accent * {
  color: hsl(327 100% 36%) !important;
}

.bg-primary,
.bg-primary * {
  background-color: hsl(327 100% 29%) !important;
}

.border-primary {
  border-color: hsl(327 100% 29%) !important;
}

.ring-primary {
  --tw-ring-color: hsl(327 100% 29%) !important;
}

/* Button overrides */
.btn-primary,
button[class*="bg-primary"] {
  background: linear-gradient(135deg, hsl(327 100% 29%), hsl(327 100% 20%)) !important;
}

.btn-primary:hover,
button[class*="bg-primary"]:hover {
  background: linear-gradient(135deg, hsl(327 100% 20%), hsl(327 100% 15%)) !important;
}

/* Badge overrides */
.badge-primary {
  background-color: hsl(327 100% 29%) !important;
  color: white !important;
}

/* Progress bar overrides */
.progress-primary [data-state="complete"] {
  background-color: hsl(327 100% 29%) !important;
}

/* Icon color overrides */
.icon-primary {
  color: hsl(327 100% 29%) !important;
}

/* Card accent overrides */
.card-primary-accent {
  border-left: 4px solid hsl(327 100% 29%) !important;
}

/* Ensure all primary elements use the correct color */
[class*="text-primary"] {
  color: hsl(327 100% 29%) !important;
}

[class*="bg-primary"] {
  background-color: hsl(327 100% 29%) !important;
}

[class*="border-primary"] {
  border-color: hsl(327 100% 29%) !important;
}

/* Enhanced Sidebar Styling */
.sidebar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.sidebar:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Navigation item hover effects */
.sidebar .group:hover {
  transform: translateX(2px);
}

.sidebar .group:hover svg {
  transform: scale(1.1);
}

/* Active navigation item glow effect */
.sidebar a[class*="bg-gradient-to-r"] {
  box-shadow: 0 4px 15px rgba(151, 0, 94, 0.3);
}

/* Ensure active navigation text is always visible */
.sidebar a[class*="bg-gradient-to-r"] * {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Active navigation item contrast enhancement */
.sidebar a[class*="bg-gradient-to-r"] svg {
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
}

/* Smooth transitions for all sidebar elements */
.sidebar * {
  transition: all 0.2s ease-in-out;
}

/* Enhanced scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(151, 0, 94, 0.2);
  border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(151, 0, 94, 0.4);
}

/* Sidebar expansion animation */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar-container .sidebar a span {
  animation: slideInFromLeft 0.3s ease-out;
}

/* Icon pulse effect on hover */
@keyframes iconPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.sidebar .group:hover svg {
  animation: iconPulse 0.3s ease-in-out;
}

/* Gradient background for better visual hierarchy */
.sidebar-container {
  position: relative;
}

.sidebar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(151, 0, 94, 0.02) 0%, rgba(100, 0, 62, 0.05) 100%);
  pointer-events: none;
  z-index: 1;
}

.sidebar {
  position: relative;
  z-index: 2;
}

/* Force visibility for active navigation items */
.sidebar a.active,
.sidebar a[aria-current="page"],
.sidebar a[class*="from-primary"] {
  background: linear-gradient(135deg, #97005e 0%, #64003e 100%) !important;
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.sidebar a.active *,
.sidebar a[aria-current="page"] *,
.sidebar a[class*="from-primary"] * {
  color: white !important;
  fill: white !important;
}

/* Enhanced active state visibility */
.sidebar a.active svg,
.sidebar a[aria-current="page"] svg,
.sidebar a[class*="from-primary"] svg {
  color: white !important;
  stroke: white !important;
}

/* Specific NavLink active state styling */
.sidebar a[class*="bg-gradient-to-r"] {
  background: linear-gradient(135deg, #97005e 0%, #64003e 100%) !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.sidebar a[class*="bg-gradient-to-r"] span {
  color: white !important;
  font-weight: 600 !important;
}

.sidebar a[class*="bg-gradient-to-r"] svg {
  color: white !important;
  filter: brightness(1.1);
}

/* Ensure proper contrast for active items */
.sidebar .group[class*="from-primary"] {
  background: linear-gradient(135deg, #97005e 0%, #64003e 100%) !important;
}

.sidebar .group[class*="from-primary"] * {
  color: white !important;
}
